"use client";

const ProjectContent = ({ project, opacity = 1, maskProgress = 0 }) => {
  return (
    <div 
      className="absolute inset-0 w-full h-full"
      style={{
        opacity: opacity,
        // Mask will be applied here - for now just opacity
        clipPath: `inset(0 ${(1 - maskProgress) * 100}% 0 0)`
      }}
    >
      {/* Visual showcase content */}
      <div className="p-8 h-full flex items-center justify-center bg-primary rounded-3xl">
        <div className="text-center">
          {/* Project preview image area */}
          <div className="aspect-square bg-primary/20 rounded-2xl mb-6 flex items-center justify-center overflow-hidden mx-auto max-w-xs">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
            {/* Future: Replace with actual project image */}
            {/* <img src={project.image} alt={project.title} className="w-full h-full object-cover" /> */}
          </div>

          {/* Project showcase details */}
          <div className="space-y-2">
            <div className="text-sm text-secondary">Live Demo</div>
            <div className="text-sm text-secondary">Gallery • Screenshots</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectContent;
