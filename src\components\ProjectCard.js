"use client";

import ProjectContent from './ProjectContent';

const ProjectCard = ({ scrollProgress, projects }) => {
  // Calculate which project we're currently showing
  const projectCount = projects.length;
  const segmentSize = 1 / projectCount;

  // Handle completion state - when scrollProgress is 1.0, show the last project fully
  let currentProjectIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show last project at final position
    currentProjectIndex = projectCount - 1;
    segmentProgress = 1.0;
  } else {
    currentProjectIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentProjectIndex = Math.min(currentProjectIndex, projectCount - 1);
  }

  const activeProjectIndex = currentProjectIndex;

  // First card slides in from right (like before)
  let cardOpacity = 0;
  let cardTranslateX = 200;

  if (activeProjectIndex >= 0) {
    cardOpacity = 1;
    // Only the first project triggers the card slide-in
    if (activeProjectIndex === 0) {
      cardTranslateX = Math.max(0, 200 - (segmentProgress * 200));
    } else {
      cardTranslateX = 0; // Card stays in position for subsequent projects
    }
  }

  return (
    <div className="w-full h-full relative bg-background flex items-center justify-center p-4">
      {/* Single card frame that slides in once */}
      <div
        className="absolute rounded-3xl border border-secondary/25 overflow-hidden"
        style={{
          opacity: cardOpacity,
          transform: `translateX(${cardTranslateX}%)`,
          zIndex: 1,
          // Responsive sizing within 90% container: use as much space as possible while maintaining 4:3 aspect ratio
          width: 'min(100%, calc((100vh - 8rem) * 4/3))', // Width constrained by height
          height: 'min(calc(100vw * 0.9 * 0.67 * 3/4), calc(100vh - 8rem))', // Height constrained by container width (90% * 2/3)
          aspectRatio: '4/3'
        }}
      >
        {/* Render all project content inside the single frame */}
        {projects.map((project, index) => {
          let contentOpacity = 0;
          let maskProgress = 0;

          // Special handling for the very first project during card slide-in
          if (activeProjectIndex === 0 && index === 0) {
            // First project: only show when card is mostly slid in (after 80% of segment)
            if (segmentProgress >= 0.8) {
              contentOpacity = 1;
              maskProgress = 1;
            } else {
              contentOpacity = 0;
              maskProgress = 0;
            }
          } else if (activeProjectIndex === 0 && index === 1) {
            // Second project: start revealing after first project is fully visible
            if (segmentProgress >= 0.8) {
              contentOpacity = 1;
              const revealProgress = (segmentProgress - 0.8) / 0.2; // 0-1 in the last 20%
              maskProgress = revealProgress;
            } else {
              contentOpacity = 0;
              maskProgress = 0;
            }
          } else if (index < activeProjectIndex) {
            // Previous projects - not visible (covered by current)
            contentOpacity = 0;
            maskProgress = 1;
          } else if (index === activeProjectIndex) {
            // Current project - fully visible
            contentOpacity = 1;
            maskProgress = 1;
          } else if (index === activeProjectIndex + 1) {
            // Next project - being revealed by mask, but only after 50% of segment
            contentOpacity = 1;
            // Add delay: mask only starts revealing after 50% of the segment
            const delayedProgress = Math.max(0, (segmentProgress - 0.5) / 0.5);
            maskProgress = delayedProgress; // Reveals from right to left in the second half of segment
          } else {
            // Future projects - not visible yet
            contentOpacity = 0;
            maskProgress = 0;
          }

          return (
            <ProjectContent
              key={project.id}
              project={project}
              projects={projects}
              activeProjectIndex={activeProjectIndex}
              opacity={contentOpacity}
              maskProgress={maskProgress}
            />
          );
        })}
      </div>
    </div>
  );
};

export default ProjectCard;
