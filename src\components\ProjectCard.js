"use client";

import ProjectContent from './ProjectContent';

const ProjectCard = ({ scrollProgress, projects }) => {
  // Calculate which project we're currently showing
  const projectCount = projects.length;
  const segmentSize = 1 / projectCount;

  // Handle completion state - when scrollProgress is 1.0, show the last project fully
  let currentProjectIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show last project at final position
    currentProjectIndex = projectCount - 1;
    segmentProgress = 1.0;
  } else {
    currentProjectIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentProjectIndex = Math.min(currentProjectIndex, projectCount - 1);
  }

  const activeProjectIndex = currentProjectIndex;

  // First card slides in from right (like before)
  let cardOpacity = 0;
  let cardTranslateX = 200;

  if (activeProjectIndex >= 0) {
    cardOpacity = 1;
    // Only the first project triggers the card slide-in
    if (activeProjectIndex === 0) {
      cardTranslateX = Math.max(0, 200 - (segmentProgress * 200));
    } else {
      cardTranslateX = 0; // Card stays in position for subsequent projects
    }
  }

  return (
    <div className="w-full h-full relative bg-background flex items-center justify-center p-4">
      {/* Single card frame that slides in once */}
      <div
        className="absolute rounded-3xl border border-secondary/25 overflow-hidden"
        style={{
          opacity: cardOpacity,
          transform: `translateX(${cardTranslateX}%)`,
          zIndex: 1,
          // Responsive sizing within 90% container: use as much space as possible while maintaining 4:3 aspect ratio
          width: 'min(100%, calc((100vh - 8rem) * 4/3))', // Width constrained by height
          height: 'min(calc(100vw * 0.9 * 0.67 * 3/4), calc(100vh - 8rem))', // Height constrained by container width (90% * 2/3)
          aspectRatio: '4/3'
        }}
      >
        {/* Render all project content inside the single frame */}
        {projects.map((project, index) => {
          let contentOpacity = 0;
          let maskProgress = 0;

          // Copy the EXACT logic from ProjectText.js for timing
          const segmentStart = index * segmentSize;
          const segmentEnd = (index + 1) * segmentSize;

          // Project content appears when card is 50% in (same as text)
          if (scrollProgress >= segmentStart + (segmentSize * 0.5) && scrollProgress < segmentEnd) {
            // Project is fully visible during its segment
            contentOpacity = 1;
            maskProgress = 1;
          }
          // Handle fade-out and mask transition when next project starts
          else if (scrollProgress >= segmentEnd && index < projectCount - 1) {
            // Current project stays visible but next project starts revealing via mask
            const fadeOutStart = segmentEnd + (segmentSize * 0.3);
            const fadeOutEnd = segmentEnd + (segmentSize * 0.5);

            if (scrollProgress <= fadeOutEnd) {
              // Keep current project visible during transition
              contentOpacity = 1;
              maskProgress = 1;
            } else {
              // Current project disappears after transition
              contentOpacity = 0;
              maskProgress = 1;
            }
          }
          // Handle next project revealing via mask
          else if (index > 0 && scrollProgress >= (index - 1) * segmentSize + segmentSize) {
            const prevSegmentEnd = index * segmentSize;
            const maskStart = prevSegmentEnd + (segmentSize * 0.3);
            const maskEnd = prevSegmentEnd + (segmentSize * 0.5);

            if (scrollProgress >= maskStart && scrollProgress <= maskEnd) {
              // Next project being revealed by mask
              contentOpacity = 1;
              const maskTransitionProgress = (scrollProgress - maskStart) / (segmentSize * 0.2);
              maskProgress = Math.min(1, maskTransitionProgress);
            } else if (scrollProgress > maskEnd) {
              // Next project fully revealed
              contentOpacity = 1;
              maskProgress = 1;
            }
          }
          // For the last project, stay visible once it appears
          else if (index === projectCount - 1 && scrollProgress >= segmentStart + (segmentSize * 0.5)) {
            contentOpacity = 1;
            maskProgress = 1;
          }
          // Handle completion state - when scrollProgress >= 1.0, keep last project visible
          else if (scrollProgress >= 1.0 && index === projectCount - 1) {
            contentOpacity = 1;
            maskProgress = 1;
          }

          return (
            <ProjectContent
              key={project.id}
              project={project}
              projects={projects}
              activeProjectIndex={activeProjectIndex}
              opacity={contentOpacity}
              maskProgress={maskProgress}
            />
          );
        })}
      </div>
    </div>
  );
};

export default ProjectCard;
