"use client";

import ProjectContent from './ProjectContent';

const ProjectCard = ({ scrollProgress, projects }) => {
  // Calculate which project we're currently showing
  const projectCount = projects.length;
  const segmentSize = 1 / projectCount;

  // Handle completion state - when scrollProgress is 1.0, show the last project fully
  let currentProjectIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show last project at final position
    currentProjectIndex = projectCount - 1;
    segmentProgress = 1.0;
  } else {
    currentProjectIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentProjectIndex = Math.min(currentProjectIndex, projectCount - 1);
  }

  const activeProjectIndex = currentProjectIndex;

  // First card slides in from right (like before)
  let cardOpacity = 0;
  let cardTranslateX = 200;

  if (activeProjectIndex >= 0) {
    cardOpacity = 1;
    // Only the first project triggers the card slide-in
    if (activeProjectIndex === 0) {
      cardTranslateX = Math.max(0, 200 - (segmentProgress * 200));
    } else {
      cardTranslateX = 0; // Card stays in position for subsequent projects
    }
  }

  return (
    <div className="w-full h-full relative bg-background flex items-center justify-center p-4">
      {/* Single card frame that slides in once */}
      <div
        className="absolute rounded-3xl border border-secondary/25 overflow-hidden"
        style={{
          opacity: cardOpacity,
          transform: `translateX(${cardTranslateX}%)`,
          zIndex: 1,
          // Responsive sizing within 90% container: use as much space as possible while maintaining 4:3 aspect ratio
          width: 'min(100%, calc((100vh - 8rem) * 4/3))', // Width constrained by height
          height: 'min(calc(100vw * 0.9 * 0.67 * 3/4), calc(100vh - 8rem))', // Height constrained by container width (90% * 2/3)
          aspectRatio: '4/3'
        }}
      >
        {/* Render all project content inside the single frame */}
        {projects.map((project, index) => {
          let contentOpacity = 0;
          let maskProgress = 0;

          // Calculate which project should be showing based on scroll progress
          // But account for the card slide-in phase
          const adjustedScrollProgress = scrollProgress;
          const adjustedProjectIndex = Math.floor(adjustedScrollProgress / segmentSize);
          const adjustedSegmentProgress = (adjustedScrollProgress % segmentSize) / segmentSize;

          if (index < adjustedProjectIndex) {
            // Previous projects - not visible (covered by current)
            contentOpacity = 0;
            maskProgress = 1;
          } else if (index === adjustedProjectIndex) {
            // Current project - fully visible, but only after card slide-in for first project
            if (index === 0) {
              // First project: wait for card to slide in (80% of segment)
              if (adjustedSegmentProgress >= 0.8) {
                contentOpacity = 1;
                maskProgress = 1;
              } else {
                contentOpacity = 0;
                maskProgress = 0;
              }
            } else {
              // All other projects: show immediately when they become active
              contentOpacity = 1;
              maskProgress = 1;
            }
          } else if (index === adjustedProjectIndex + 1) {
            // Next project - being revealed by mask
            contentOpacity = 1;
            if (adjustedProjectIndex === 0) {
              // Transition from first to second project: start after first is visible
              if (adjustedSegmentProgress >= 0.8) {
                const revealProgress = (adjustedSegmentProgress - 0.8) / 0.2;
                maskProgress = revealProgress;
              } else {
                maskProgress = 0;
              }
            } else {
              // All other transitions: start after 50% of segment
              const delayedProgress = Math.max(0, (adjustedSegmentProgress - 0.5) / 0.5);
              maskProgress = delayedProgress;
            }
          } else {
            // Future projects - not visible yet
            contentOpacity = 0;
            maskProgress = 0;
          }

          return (
            <ProjectContent
              key={project.id}
              project={project}
              projects={projects}
              activeProjectIndex={activeProjectIndex}
              opacity={contentOpacity}
              maskProgress={maskProgress}
            />
          );
        })}
      </div>
    </div>
  );
};

export default ProjectCard;
